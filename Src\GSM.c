/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    GSM.c
  * @brief   GSM模块AT指令控制实现
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "GSM.h"
#include "gpio.h"
#include "globals.h"
#include <string.h>
#include <stdio.h>
#include <stdlib.h>

/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/* 私有变量定义 */
static GSM_State_t gsm_state = GSM_STATE_INIT;
static char gsm_rx_buffer[GSM_RX_BUFFER_SIZE];  // 接收缓冲区

/* 中断缓冲区大小定义（与main.c保持一致） */
#define GSM_INTERRUPT_BUFFER_SIZE 512

/* 外部变量声明 */
extern char network_cmd_saved[];
extern uint8_t network_cmd_new_data;

/* 私有函数声明 */
static void GSM_PrintResponse(const char* command, const char* response);

/* USER CODE BEGIN 1 */

/**
 * @brief  GSM电源开启
 */
void GSM_PowerOn(void)
{
    RF_PWR_ON;  // 使用gpio.h中定义的宏
}

/**
 * @brief  GSM电源关闭
 */
void GSM_PowerOff(void)
{
    RF_PWR_OFF;  // 使用gpio.h中定义的宏
}

/**
 * @brief  GSM完整初始化流程（包含电源控制和等待）
 * @retval GSM_Status_t 初始化状态
 */
GSM_Status_t GSM_FullInit(void)
{
    // 开启GSM电源
    GSM_PowerOn();

    // 等待GSM模块启动（至少5秒）
    HAL_Delay(GSM_POWER_ON_DELAY_MS);

    // 执行GSM初始化
    GSM_Status_t status = GSM_Init();

    return status;
}

/* USER CODE END 1 */

/**
 * @brief  GSM模块初始化
 * @retval GSM_Status_t 初始化状态
 */
GSM_Status_t GSM_Init(void)
{
    GSM_Status_t status;

    // 重置接收缓冲区
    memset(gsm_rx_buffer, 0, sizeof(gsm_rx_buffer));

    // 1. 关闭回显
    status = GSM_SendATCommandWithRetry("ATE0", "OK", GSM_AT_TIMEOUT_MS, GSM_AT_RETRY_COUNT);
    if (status != GSM_OK) {
        gsm_state = GSM_STATE_ERROR;
        return status;
    }

    // 2. 获取CCID号并保存到全局变量
    status = GSM_SendATCommandWithRetry("AT+CCID", "OK", GSM_AT_TIMEOUT_MS, GSM_AT_RETRY_COUNT);
    if (status == GSM_OK) {
        // 解析CCID并保存到全局变量
        // 查找数字开头的CCID（通常是20位数字）
        char* ptr = gsm_rx_buffer;
        while (*ptr) {
            if (*ptr >= '0' && *ptr <= '9') {
                // 找到数字开头，提取CCID
                char* start = ptr;
                while (*ptr >= '0' && *ptr <= '9') {
                    ptr++;
                }
                uint16_t len = ptr - start;
                if (len >= 15 && len <= 20 && len < sizeof(gsm_ccid)) {  // CCID通常15-20位
                    strncpy(gsm_ccid, start, len);
                    gsm_ccid[len] = '\0';
                    break;
                }
            }
            ptr++;
        }
    }

    // 3. 获取模块电压
    uint16_t voltage = 0;
    GSM_GetVoltage(&voltage);

    // 4. 查询网络注册状态
    uint8_t network_reg = 0;
    GSM_CheckNetwork(&network_reg);

    // 5. 获取信号强度并保存到全局变量
    uint8_t signal = 0;
    status = GSM_GetSignal(&signal);
    if (status == GSM_OK) {
        gsm_signal_quality = (int8_t)signal;
    } else {
        gsm_signal_quality = -128;  // 表示无效
    }

    gsm_state = GSM_STATE_READY;
    return GSM_OK;
}

/**
 * @brief  关闭回显
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_CloseEcho(void)
{
    return GSM_SendATCommand("ATE0", "OK", GSM_AT_TIMEOUT_MS);
}

/**
 * @brief  获取模块型号
 * @param  model: 型号字符串缓冲区
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_GetModel(char* model)
{
    GSM_Status_t status = GSM_SendATCommand("AT+CGMM", "OK", GSM_AT_TIMEOUT_MS);
    if (status == GSM_OK) {
        // 解析响应中的型号信息
        char* start = strstr(gsm_rx_buffer, "+CGMM: ");
        if (start) {
            start += 7;  // 跳过"+CGMM: "
            char* end = strchr(start, '\r');
            if (end) {
                uint16_t len = end - start;
                if (len > 0) {
                    strncpy(model, start, len);
                    model[len] = '\0';
                }
            }
        }
    }
    return status;
}

/**
 * @brief  获取CCID号
 * @param  ccid: CCID字符串缓冲区
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_GetCCID(char* ccid)
{
    GSM_Status_t status = GSM_SendATCommand("AT+CCID", "OK", GSM_AT_TIMEOUT_MS);
    if (status == GSM_OK) {
        // 解析响应中的CCID信息
        char* start = strstr(gsm_rx_buffer, "AT+CCID");
        if (start) {
            start = strchr(start, '\r');
            if (start) {
                start = strchr(start + 1, '\r');
                if (start) {
                    start += 2;  // 跳过"\r\n"
                    char* end = strchr(start, '\r');
                    if (end) {
                        uint16_t len = end - start;
                        if (len > 0 && len < 32) {
                            strncpy(ccid, start, len);
                            ccid[len] = '\0';
                        }
                    }
                }
            }
        }
    }
    return status;
}

/**
 * @brief  获取模块电压
 * @param  voltage: 电压值指针(mV)
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_GetVoltage(uint16_t* voltage)
{
    GSM_Status_t status = GSM_SendATCommand("AT+CBC", "OK", GSM_AT_TIMEOUT_MS);
    if (status == GSM_OK) {
        // 解析响应中的电压信息
        char* start = strstr(gsm_rx_buffer, "+CBC: ");
        if (start) {
            start += 6;  // 跳过"+CBC: "
            *voltage = (uint16_t)atoi(start);
        }
    }
    return status;
}

/**
 * @brief  查询网络注册状态
 * @param  reg_status: 注册状态指针
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_CheckNetwork(uint8_t* reg_status)
{
    GSM_Status_t status = GSM_SendATCommand("AT+CREG?", "OK", GSM_AT_TIMEOUT_MS);
    if (status == GSM_OK) {
        // 解析响应中的注册状态
        char* start = strstr(gsm_rx_buffer, "+CREG: ");
        if (start) {
            start += 7;  // 跳过"+CREG: "
            char* comma = strchr(start, ',');
            if (comma) {
                comma++;  // 跳过逗号
                *reg_status = (uint8_t)atoi(comma);
            }
        }
    }
    return status;
}

/**
 * @brief  获取信号强度
 * @param  signal: 信号强度指针(0-31)
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_GetSignal(uint8_t* signal)
{
    GSM_Status_t status = GSM_SendATCommand("AT+CSQ", "OK", GSM_AT_TIMEOUT_MS);
    if (status == GSM_OK) {
        // 解析响应中的信号强度
        char* start = strstr(gsm_rx_buffer, "+CSQ: ");
        if (start) {
            start += 6;  // 跳过"+CSQ: "
            char* comma = strchr(start, ',');
            if (comma) {
                *comma = '\0';  // 临时结束符
                *signal = (uint8_t)atoi(start);
            }
        }
    }
    return status;
}

/**
 * @brief  连接TCP服务器
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_ConnectServer(void)
{
    char command[128];
    sprintf(command, "AT+CIPSTART=\"TCP\",\"%s\",%s", GSM_SERVER_IP, GSM_SERVER_PORT);

    // 步骤1: 发送连接指令，先等待"OK"响应
    GSM_Status_t status = GSM_SendATCommand(command, "OK", 5000);
    if (status != GSM_OK) {
        return status;
    }

    // 步骤2: 等待"CONNECT OK"响应（可能需要更长时间）
    extern char gsm_interrupt_buffer[];
    extern uint16_t gsm_interrupt_buffer_index;
    extern uint8_t gsm_response_ready;

    // 清空中断接收缓冲区
    gsm_interrupt_buffer_index = 0;
    gsm_response_ready = 0;
    memset(gsm_interrupt_buffer, 0, GSM_INTERRUPT_BUFFER_SIZE);

    uint32_t start_time = HAL_GetTick();
    while ((HAL_GetTick() - start_time) < 10000) {  // 等待10秒
        if (gsm_response_ready) {
            // 复制数据到接收缓冲区
            memcpy(gsm_rx_buffer, gsm_interrupt_buffer, gsm_interrupt_buffer_index);
            gsm_rx_buffer[gsm_interrupt_buffer_index] = '\0';

            printf("GSM connect response: %s", gsm_rx_buffer);

            // 清除中断缓冲区状态
            gsm_response_ready = 0;
            gsm_interrupt_buffer_index = 0;
            memset(gsm_interrupt_buffer, 0, GSM_INTERRUPT_BUFFER_SIZE);

            // 检查连接结果
            if (strstr(gsm_rx_buffer, "CONNECT OK") != NULL) {
                gsm_state = GSM_STATE_CONNECTED;


                // 连接成功后，检查是否有服务器下发指令
                if (strstr(gsm_rx_buffer, "ZL+") != NULL) {
                    char* zl_pos = strstr(gsm_rx_buffer, "ZL+");

                    // 触发网络指令处理标志
                    strncpy(network_cmd_saved, zl_pos, 31);
                    network_cmd_saved[31] = '\0';
                    // 移除可能的换行符
                    char* newline = strchr(network_cmd_saved, '\r');
                    if (newline) *newline = '\0';
                    newline = strchr(network_cmd_saved, '\n');
                    if (newline) *newline = '\0';
                    network_cmd_new_data = 1;
                }

                // 连接成功后，额外等待2秒检查服务器是否下发指令
                HAL_Delay(2000);

                // 再次检查中断缓冲区是否有新数据



                if (gsm_response_ready) {
                    memcpy(gsm_rx_buffer, gsm_interrupt_buffer, gsm_interrupt_buffer_index);
                    gsm_rx_buffer[gsm_interrupt_buffer_index] = '\0';

                    printf("Additional server response: %s", gsm_rx_buffer);

                    // 检查是否有服务器下发指令
                    if (strstr(gsm_rx_buffer, "ZL+") != NULL) {
                        char* zl_pos = strstr(gsm_rx_buffer, "ZL+");

                        // 触发网络指令处理标志
                        strncpy(network_cmd_saved, zl_pos, 31);
                        network_cmd_saved[31] = '\0';
                        // 移除可能的换行符
                        char* newline = strchr(network_cmd_saved, '\r');
                        if (newline) *newline = '\0';
                        newline = strchr(network_cmd_saved, '\n');
                        if (newline) *newline = '\0';
                        network_cmd_new_data = 1;
                    }

                    // 清除中断缓冲区状态
                    gsm_response_ready = 0;
                    gsm_interrupt_buffer_index = 0;
                    memset(gsm_interrupt_buffer, 0, GSM_INTERRUPT_BUFFER_SIZE);
                }

                return GSM_OK;
            } else if (strstr(gsm_rx_buffer, "ALREADY CONNECT") != NULL) {
                gsm_state = GSM_STATE_CONNECTED;

                return GSM_OK;
            } else if (strstr(gsm_rx_buffer, "ERROR") != NULL ||
                       strstr(gsm_rx_buffer, "FAIL") != NULL) {

                return GSM_ERROR;
            }
            break;
        }
        HAL_Delay(10);
    }


    return GSM_TIMEOUT;
}

/**
 * @brief  关闭服务器连接
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_CloseServer(void)
{
    // 使用新的同步AT指令机制
    GSM_Status_t status = GSM_SendATCommand("AT+CIPCLOSE", "CLOSE OK", 5000);

    // 无论是否成功，都将状态设为READY（连接已断开）
    gsm_state = GSM_STATE_READY;

    if (status == GSM_OK) {
        return GSM_OK;
    }

    // 检查其他可能的成功响应
    if (strstr(gsm_rx_buffer, "OK") != NULL ||
        strstr(gsm_rx_buffer, "CLOSED") != NULL) {
        return GSM_OK;
    }

    // 即使失败也返回OK，因为连接状态已重置
    return GSM_OK;
}

/**
 * @brief  设置非透传快发模式
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_SetQuickSend(void)
{
    return GSM_SendATCommand("AT+CIPQSEND=1", "OK", GSM_AT_TIMEOUT_MS);
}

/**
 * @brief  发送数据
 * @param  data: 要发送的数据
 * @param  length: 数据长度
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_SendData(const char* data, uint16_t length)
{
    if (gsm_state != GSM_STATE_CONNECTED) {
        printf("GSM not connected, cannot send data\r\n");
        return GSM_ERROR;
    }

    // 计算实际数据长度
    uint16_t actual_length = strlen(data);

    // 步骤1: 发送AT+CIPSEND=长度指令，等待">"提示符
    char command[32];
    sprintf(command, "AT+CIPSEND=%d", actual_length);

    GSM_Status_t status = GSM_SendATCommand(command, ">", 5000);
    if (status != GSM_OK) {
        return GSM_ERROR;
    }

    // 步骤2: 发送实际数据
    HAL_UART_Transmit(&hlpuart1, (uint8_t*)data, actual_length, 5000);


    // 步骤3: 等待发送完成确认（DATA ACCEPT）
    extern char gsm_interrupt_buffer[];
    extern uint16_t gsm_interrupt_buffer_index;
    extern uint8_t gsm_response_ready;

    // 清空中断接收缓冲区
    gsm_interrupt_buffer_index = 0;
    gsm_response_ready = 0;
    memset(gsm_interrupt_buffer, 0, GSM_INTERRUPT_BUFFER_SIZE);

    uint32_t start_time = HAL_GetTick();
    while ((HAL_GetTick() - start_time) < 10000) {  // 等待10秒
        if (gsm_response_ready) {
            // 复制数据到接收缓冲区
            memcpy(gsm_rx_buffer, gsm_interrupt_buffer, gsm_interrupt_buffer_index);
            gsm_rx_buffer[gsm_interrupt_buffer_index] = '\0';

            printf("GSM data response: %s", gsm_rx_buffer);

            // 清除中断缓冲区状态
            gsm_response_ready = 0;
            gsm_interrupt_buffer_index = 0;
            memset(gsm_interrupt_buffer, 0, GSM_INTERRUPT_BUFFER_SIZE);

            // 检查是否收到确认
            if (strstr(gsm_rx_buffer, "DATA ACCEPT:") != NULL) {


                // 检查ACCEPT后是否有服务器下发指令
                if (strstr(gsm_rx_buffer, "ZL+") != NULL) {
                    char* zl_pos = strstr(gsm_rx_buffer, "ZL+");

                    // 触发网络指令处理标志
                    strncpy(network_cmd_saved, zl_pos, 31);
                    network_cmd_saved[31] = '\0';
                    // 移除可能的换行符
                    char* newline = strchr(network_cmd_saved, '\r');
                    if (newline) *newline = '\0';
                    newline = strchr(network_cmd_saved, '\n');
                    if (newline) *newline = '\0';
                    network_cmd_new_data = 1;
                }

                return GSM_OK;
            }
            // 检查是否只收到ACCEPT（服务器确认）
            else if (strstr(gsm_rx_buffer, "ACCEPT") != NULL) {


                // 检查ACCEPT后是否有服务器下发指令
                if (strstr(gsm_rx_buffer, "ZL+") != NULL) {
                    char* zl_pos = strstr(gsm_rx_buffer, "ZL+");

                    // 触发网络指令处理标志
                    strncpy(network_cmd_saved, zl_pos, 31);
                    network_cmd_saved[31] = '\0';
                    // 移除可能的换行符
                    char* newline = strchr(network_cmd_saved, '\r');
                    if (newline) *newline = '\0';
                    newline = strchr(network_cmd_saved, '\n');
                    if (newline) *newline = '\0';
                    network_cmd_new_data = 1;
                }

                // 额外等待1秒，检查是否还有后续指令
                HAL_Delay(1000);
                if (gsm_response_ready) {
                    memcpy(gsm_rx_buffer, gsm_interrupt_buffer, gsm_interrupt_buffer_index);
                    gsm_rx_buffer[gsm_interrupt_buffer_index] = '\0';

                    printf("Additional response after ACCEPT: %s", gsm_rx_buffer);

                    // 检查是否有服务器下发指令
                    if (strstr(gsm_rx_buffer, "ZL+") != NULL) {
                        char* zl_pos = strstr(gsm_rx_buffer, "ZL+");

                        strncpy(network_cmd_saved, zl_pos, 31);
                        network_cmd_saved[31] = '\0';
                        // 移除可能的换行符
                        char* newline = strchr(network_cmd_saved, '\r');
                        if (newline) *newline = '\0';
                        newline = strchr(network_cmd_saved, '\n');
                        if (newline) *newline = '\0';
                        network_cmd_new_data = 1;
                    }

                    // 清除中断缓冲区状态
                    gsm_response_ready = 0;
                    gsm_interrupt_buffer_index = 0;
                    memset(gsm_interrupt_buffer, 0, GSM_INTERRUPT_BUFFER_SIZE);
                }

                return GSM_OK;
            }
            break;
        }
        HAL_Delay(10);
    }


    return GSM_ERROR;
}

/**
 * @brief  获取模块信息
 * @param  info: 模块信息结构体指针
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_GetInfo(GSM_Info_t* info)
{
    GSM_Status_t status;

    // 获取模块型号
    status = GSM_GetModel(info->model);
    if (status != GSM_OK) return status;

    // 获取CCID号
    status = GSM_GetCCID(info->ccid);
    if (status != GSM_OK) return status;

    // 获取电压
    status = GSM_GetVoltage(&info->voltage);
    if (status != GSM_OK) return status;

    // 获取信号强度
    status = GSM_GetSignal(&info->signal);
    if (status != GSM_OK) return status;

    // 获取网络注册状态
    status = GSM_CheckNetwork(&info->network_reg);

    return status;
}

/**
 * @brief  获取当前状态
 * @retval GSM_State_t 当前状态
 */
GSM_State_t GSM_GetState(void)
{
    return gsm_state;
}

/**
 * @brief  带重试机制的AT指令发送
 * @param  command: AT指令
 * @param  expected_response: 期望的响应
 * @param  timeout: 超时时间(毫秒)
 * @param  retry_count: 重试次数
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_SendATCommandWithRetry(const char* command, const char* expected_response, uint32_t timeout, uint8_t retry_count)
{
    GSM_Status_t status;
    uint8_t attempts = 0;

    do {
        attempts++;

        status = GSM_SendATCommand(command, expected_response, timeout);

        if (status == GSM_OK) {
            GSM_PrintResponse(command, gsm_rx_buffer);
            return GSM_OK;
        } else {
            if (attempts <= retry_count) {
                HAL_Delay(1000);  // 重试前等待1秒
            }
        }
    } while (attempts <= retry_count);

    return status;
}

/**
 * @brief  发送AT指令并等待响应（中断接收方式）
 * @param  command: AT指令
 * @param  expected_response: 期望的响应
 * @param  timeout: 超时时间(毫秒)
 * @retval GSM_Status_t 执行状态
 */
GSM_Status_t GSM_SendATCommand(const char* command, const char* expected_response, uint32_t timeout)
{
    extern char gsm_interrupt_buffer[];
    extern uint16_t gsm_interrupt_buffer_index;
    extern uint8_t gsm_response_ready;

    // 清空中断接收缓冲区
    gsm_interrupt_buffer_index = 0;
    gsm_response_ready = 0;
    memset(gsm_interrupt_buffer, 0, GSM_INTERRUPT_BUFFER_SIZE);
    memset(gsm_rx_buffer, 0, sizeof(gsm_rx_buffer));

    // 发送AT指令
    if (strlen(command) > 0) {
        HAL_UART_Transmit(&hlpuart1, (uint8_t*)command, strlen(command), 1000);
        HAL_UART_Transmit(&hlpuart1, (uint8_t*)"\r\n", 2, 1000);
        printf("GSM send: %s\r\n", command);
    }

    // 等待中断接收响应
    uint32_t start_time = HAL_GetTick();
    while ((HAL_GetTick() - start_time) < timeout) {
        if (gsm_response_ready) {
            // 复制中断缓冲区数据
            uint16_t copy_length = gsm_interrupt_buffer_index;
            if (copy_length >= GSM_RX_BUFFER_SIZE) {
                copy_length = GSM_RX_BUFFER_SIZE - 1;
            }
            memcpy(gsm_rx_buffer, gsm_interrupt_buffer, copy_length);
            gsm_rx_buffer[copy_length] = '\0';

            printf("GSM recv: %s", gsm_rx_buffer);

            // 清除中断缓冲区状态
            gsm_response_ready = 0;
            gsm_interrupt_buffer_index = 0;
            memset(gsm_interrupt_buffer, 0, GSM_INTERRUPT_BUFFER_SIZE);

            // 检查响应
            if (strstr(gsm_rx_buffer, expected_response) != NULL) {
                return GSM_OK;
            }
            if (strstr(gsm_rx_buffer, "ERROR") != NULL) {
                return GSM_ERROR;
            }
            break;
        }
        HAL_Delay(10);  // 短暂延时避免CPU占用过高
    }

    // 超时处理
    if (strlen(command) > 0) {
        printf("GSM timeout: %s\r\n", command);
    }
    return GSM_TIMEOUT;
}



/**
 * @brief  启动网络指令监听（现在由中断处理）
 */
void GSM_StartNetworkCommandListener(void)
{
    // 网络指令监听已通过中断模式启动
}



/**
 * @brief  打印AT指令响应
 * @param  command: AT指令
 * @param  response: 响应内容
 */
static void GSM_PrintResponse(const char* command, const char* response)
{
    printf("AT Response [%s]: %s\r\n", command, response);
}



/* USER CODE BEGIN 2 */

/* USER CODE END 2 */
